"use client";

import { useMemo } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Edit, Trash2 } from "lucide-react";
import { useAdminStore } from "@/store/adminStore";

export function ArticlesTable() {
  const { articles, workshops, searchQuery, handleOpenEditModal, handleDelete } = useAdminStore();

  const filteredArticles = useMemo(() => {
    const onlyArticles = articles.filter(
      (article) => article.type === "article"
    );
    if (!searchQuery) return onlyArticles;
    return onlyArticles.filter((article) =>
      article.title.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [articles, searchQuery]);

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead className="w-[100px]">ID</TableHead>
          <TableHead>Title</TableHead>
          <TableHead>Workshop</TableHead>
          <TableHead>Created At</TableHead>
          <TableHead>Updated At</TableHead>
          <TableHead className="text-right">Actions</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {filteredArticles.map((article) => (
          <TableRow key={article.id}>
            <TableCell className="font-medium">{article.id}</TableCell>
            <TableCell>{article.title}</TableCell>
            <TableCell>
              {article.type === "article" && article.workshop_id
                ? workshops.find((w) => w.id === article.workshop_id)?.title ||
                  "N/A"
                : "N/A"}
            </TableCell>
            <TableCell>
              {new Date(article.createdAt).toLocaleString()}
            </TableCell>
            <TableCell>
              {new Date(article.updatedAt).toLocaleString()}
            </TableCell>
            <TableCell className="text-right">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => handleOpenEditModal(article)}
              >
                <Edit className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => handleDelete(article.id)}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
} 