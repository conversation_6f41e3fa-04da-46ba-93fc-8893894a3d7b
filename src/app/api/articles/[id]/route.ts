import { NextResponse } from "next/server";
import db from "@/lib/db";
import { NextRequest } from "next/server";

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { title, content: description, type, workshop_id } = await request.json();
    const id = params.id;

    if (type === "workshop") {
      const stmt = db.prepare(
        "UPDATE workshops SET title = ?, description = ? WHERE id = ?"
      );
      stmt.run(title, description, id);
    } else {
      const stmt = db.prepare(
        "UPDATE resources SET title = ?, description = ?, workshop_id = ? WHERE id = ?"
      );
      stmt.run(title, description, workshop_id, id);
    }
    return NextResponse.json({ message: "Item updated successfully" });
  } catch (error) {
    console.error(error);
    return NextResponse.json(
      { error: "Failed to update item" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;
    // We need to know if we're deleting a workshop or a resource.
    // The admin page logic seems to imply we can tell, but the DELETE request itself doesn't.
    // Let's assume for now we try to delete from resources, and if not found, from workshops.
    // A better approach would be to pass the type in the request body or as a query param.
    
    let changes = db.prepare("DELETE FROM resources WHERE id = ?").run(id).changes;
    if (changes === 0) {
      changes = db.prepare("DELETE FROM workshops WHERE id = ?").run(id).changes;
    }

    if (changes > 0) {
      return NextResponse.json({ message: "Item deleted successfully" });
    } else {
      return NextResponse.json({ error: "Item not found" }, { status: 404 });
    }
  } catch (error) {
    console.error(error);
    return NextResponse.json(
      { error: "Failed to delete item" },
      { status: 500 }
    );
  }
} 